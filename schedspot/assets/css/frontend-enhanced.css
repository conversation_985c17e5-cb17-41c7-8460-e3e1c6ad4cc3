/* SchedSpot Enhanced Frontend Styles */

/* Modern Booking Form Styles - Matching Dashboard Design */
.schedspot-booking-form {
    max-width: 900px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.schedspot-booking-form .schedspot-navigation {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 30px;
    margin: 0;
}

.schedspot-form-container {
    padding: 30px;
}

.schedspot-form-section {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    transition: box-shadow 0.3s ease;
}

.schedspot-form-section:hover {
    box-shadow: 0 2px 12px rgba(0, 115, 170, 0.1);
}

.schedspot-form-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.schedspot-form-section h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #0073aa, #005a87);
    border-radius: 2px;
}

.schedspot-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.schedspot-form-row {
    margin-bottom: 20px;
}

.schedspot-form-row label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.schedspot-form-row input,
.schedspot-form-row select,
.schedspot-form-row textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.schedspot-form-row input:focus,
.schedspot-form-row select:focus,
.schedspot-form-row textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
    transform: translateY(-1px);
}

.schedspot-form-row textarea {
    resize: vertical;
    min-height: 100px;
}

.schedspot-form-actions {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
}

.schedspot-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 28px;
    background: linear-gradient(135deg, #0073aa, #005a87);
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.3);
}

.schedspot-btn:hover {
    background: linear-gradient(135deg, #005a87, #004a73);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 115, 170, 0.4);
    color: white;
    text-decoration: none;
}

.schedspot-btn:active {
    transform: translateY(0);
}

.schedspot-btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.schedspot-btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    box-shadow: 0 4px 16px rgba(108, 117, 125, 0.4);
}

.schedspot-btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.schedspot-btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.4);
}

.schedspot-btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    color: #212529;
}

.schedspot-btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #dc6545);
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.4);
    color: #212529;
}

.schedspot-btn-small {
    padding: 8px 16px;
    font-size: 12px;
    margin: 2px;
}

.required {
    color: #e74c3c;
    font-weight: bold;
}

/* Navigation Bar */
.schedspot-navigation {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 15px;
}

.schedspot-nav-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.schedspot-nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.schedspot-nav-link:hover {
    background: #e9ecef;
    border-color: #0073aa;
    color: #0073aa;
    text-decoration: none;
}

.schedspot-nav-link.active {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.schedspot-nav-link.admin-switcher {
    background: #dc3545;
    border-color: #dc3545;
    color: #fff;
    margin-left: auto;
}

.schedspot-nav-link.admin-switcher:hover {
    background: #c82333;
    border-color: #c82333;
}

/* Dashboard Header */
.schedspot-dashboard-header {
    margin-bottom: 30px;
}

.schedspot-dashboard-header h2 {
    margin: 0 0 5px 0;
    color: #333;
}

.schedspot-dashboard-header .user-role {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.admin-mode-indicator {
    background: #dc3545;
    color: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 10px;
}

/* Enhanced Worker Settings Modal */
.schedspot-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.schedspot-settings-modal .schedspot-modal-content {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.schedspot-modal-header {
    background: #0073aa;
    color: #fff;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.schedspot-modal-header h3 {
    margin: 0;
    color: #fff;
}

.schedspot-modal-close {
    color: #fff;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.schedspot-modal-close:hover {
    opacity: 0.7;
}

.schedspot-modal-body {
    padding: 0;
    flex: 1;
    overflow-y: auto;
}

/* Settings Tabs */
.schedspot-settings-tabs {
    display: flex;
    height: 100%;
}

.settings-tab-nav {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    width: 200px;
    padding: 0;
}

.settings-tab-btn {
    display: block;
    width: 100%;
    padding: 15px 20px;
    background: none;
    border: none;
    border-bottom: 1px solid #dee2e6;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.settings-tab-btn:hover {
    background: #e9ecef;
}

.settings-tab-btn.active {
    background: #0073aa;
    color: #fff;
}

.settings-tab-content {
    flex: 1;
    padding: 20px;
}

.settings-tab-pane {
    display: none;
}

.settings-tab-pane.active {
    display: block;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

/* Schedule Grid */
.schedule-grid {
    display: grid;
    gap: 15px;
    margin-bottom: 20px;
}

.schedule-day {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    background: #f9f9f9;
}

.schedule-day h5 {
    margin: 0 0 10px 0;
    color: #333;
}

.time-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.time-inputs input[type="time"] {
    width: auto;
    flex: 1;
}

/* Services List */
.services-list {
    margin-bottom: 20px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 10px;
    background: #f9f9f9;
}

.service-info {
    flex: 1;
}

.service-info label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

.service-info p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.service-pricing {
    width: 150px;
    margin-left: 20px;
}

.service-pricing label {
    font-size: 12px;
    margin-bottom: 5px;
}

.service-pricing input {
    width: 100%;
}

/* Earnings Summary */
.earnings-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
    margin-top: 20px;
}

.earnings-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.earnings-stats .stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.earnings-stats .label {
    font-weight: 600;
    color: #333;
}

.earnings-stats .value {
    font-weight: bold;
    color: #0073aa;
}

/* Buttons */
.schedspot-btn {
    display: inline-block;
    padding: 10px 20px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.schedspot-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.schedspot-btn-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.schedspot-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

/* Admin Dashboard */
.admin-dashboard-notice {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 30px;
}

.admin-dashboard-notice h3 {
    margin: 0 0 10px 0;
    color: #0c5460;
}

.admin-dashboard-notice p {
    margin: 0 0 15px 0;
    color: #0c5460;
}

.admin-quick-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedspot-nav-links {
        flex-direction: column;
    }
    
    .schedspot-nav-link.admin-switcher {
        margin-left: 0;
    }
    
    .schedspot-settings-tabs {
        flex-direction: column;
    }
    
    .settings-tab-nav {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .settings-tab-nav {
        display: flex;
        overflow-x: auto;
    }
    
    .settings-tab-btn {
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid #dee2e6;
    }
    
    .service-item {
        flex-direction: column;
    }
    
    .service-pricing {
        width: 100%;
        margin-left: 0;
        margin-top: 15px;
    }
    
    .earnings-stats {
        grid-template-columns: 1fr;
    }
}

/* Worker Selection Styling */
.worker-selection-mode {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.worker-selection-mode label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.worker-selection-mode label:hover {
    background: rgba(0, 115, 170, 0.05);
}

.worker-selection-mode input[type="radio"] {
    width: auto;
    margin: 0;
}

/* Payment Information Styling */
.schedspot-payment-info {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 25px;
    margin-top: 30px;
}

.schedspot-payment-info h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.schedspot-payment-info h3::before {
    content: '💳';
    font-size: 20px;
}

.payment-details {
    background: #fff;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.payment-methods {
    margin-top: 15px;
}

.payment-icons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.payment-method {
    background: #fff;
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

.security-notice {
    margin-top: 15px;
    padding: 10px;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 6px;
    text-align: center;
}

.security-notice small {
    color: #28a745;
    font-weight: 600;
}

/* Enhanced Notice Styling */
.schedspot-notice {
    padding: 16px 20px;
    margin: 20px 0;
    border-radius: 8px;
    border-left: 4px solid;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
}

.schedspot-notice-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left-color: #28a745;
}

.schedspot-notice-success::before {
    content: '✓';
    background: #28a745;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.schedspot-notice-error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-left-color: #dc3545;
}

.schedspot-notice-error::before {
    content: '⚠';
    background: #dc3545;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Animation for form sections */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.schedspot-form-section {
    animation: slideInUp 0.6s ease-out;
}

.schedspot-form-section:nth-child(2) { animation-delay: 0.1s; }
.schedspot-form-section:nth-child(3) { animation-delay: 0.2s; }
.schedspot-form-section:nth-child(4) { animation-delay: 0.3s; }

/* Loading states */
.schedspot-form-row.loading input,
.schedspot-form-row.loading select,
.schedspot-form-row.loading textarea {
    background: #f8f9fa;
    pointer-events: none;
}

.schedspot-form-row.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 15px;
    width: 16px;
    height: 16px;
    border: 2px solid #dee2e6;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Worker Cards Styling */
.schedspot-workers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.schedspot-worker-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.schedspot-worker-card:hover {
    border-color: #0073aa;
    box-shadow: 0 4px 16px rgba(0, 115, 170, 0.15);
    transform: translateY(-2px);
}

.schedspot-worker-card.selected {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9, #e8f5e8);
}

.worker-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
}

.worker-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.availability-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.availability-indicator.available {
    background: #28a745;
}

.availability-indicator.busy {
    background: #dc3545;
}

.worker-info {
    text-align: center;
}

.worker-info h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.worker-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    margin-bottom: 8px;
}

.star {
    color: #ffc107;
    font-size: 14px;
}

.star.empty {
    color: #dee2e6;
}

.star.half {
    background: linear-gradient(90deg, #ffc107 50%, #dee2e6 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.rating-count {
    font-size: 12px;
    color: #6c757d;
    margin-left: 5px;
}

.worker-rate {
    font-size: 18px;
    font-weight: 700;
    color: #0073aa;
    margin-bottom: 8px;
}

.worker-skills {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
    line-height: 1.4;
}

.worker-stats {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
    margin-bottom: 15px;
}

.worker-actions {
    text-align: center;
}

/* Field Error Styling */
.field-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.schedspot-form-row input.error,
.schedspot-form-row select.error,
.schedspot-form-row textarea.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Messaging Interface Styling */
.schedspot-messaging {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    min-height: 600px;
}

.schedspot-conversations {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    padding: 20px;
}

.conversations-header h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #0073aa;
}

.conversations-list {
    max-height: 500px;
    overflow-y: auto;
}

.conversation-item {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.conversation-item:hover {
    background: #e9ecef;
    border-color: #0073aa;
}

.conversation-item.active {
    background: #0073aa;
    color: #fff;
}

.schedspot-chat-area {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.chat-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.chat-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.schedspot-messages {
    flex: 1;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.schedspot-message {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.schedspot-message.own {
    flex-direction: row-reverse;
}

.schedspot-message .avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.schedspot-message .content {
    max-width: 70%;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
}

.schedspot-message.own .content {
    background: #0073aa;
    color: #fff;
}

.schedspot-message .text {
    margin-bottom: 5px;
    line-height: 1.4;
}

.schedspot-message .time {
    font-size: 11px;
    color: #6c757d;
    text-align: right;
}

.schedspot-message.own .time {
    color: rgba(255, 255, 255, 0.8);
}

.schedspot-attachment {
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
}

.schedspot-attachment a {
    color: #0073aa;
    text-decoration: none;
    font-size: 12px;
}

.schedspot-message-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.schedspot-message-input {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.schedspot-message-input textarea {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 10px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 20px;
    resize: none;
    font-family: inherit;
}

.schedspot-message-input textarea:focus {
    outline: none;
    border-color: #0073aa;
}

.message-actions {
    display: flex;
    gap: 5px;
    align-items: center;
}

.attachment-button {
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.attachment-button:hover {
    background: #e9ecef;
    border-color: #0073aa;
}

.send-button {
    padding: 8px 16px;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.send-button:hover {
    background: #005a87;
}

.no-conversation {
    text-align: center;
    color: #6c757d;
    padding: 40px 20px;
}

.no-messages {
    text-align: center;
    color: #6c757d;
    padding: 20px;
    font-style: italic;
}

.loading {
    text-align: center;
    color: #6c757d;
    padding: 20px;
}

/* Responsive Design for Messaging */
@media (max-width: 768px) {
    .schedspot-messaging {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .schedspot-conversations {
        max-height: 200px;
        overflow-y: auto;
    }

    .schedspot-workers-grid {
        grid-template-columns: 1fr;
    }

    .schedspot-message .content {
        max-width: 85%;
    }
}

/* Booking Details Modal */
.schedspot-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.schedspot-modal {
    background: #fff;
    border-radius: 12px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.schedspot-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.schedspot-modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
}

.close-booking-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-booking-modal:hover {
    background: #e9ecef;
    color: #495057;
}

.schedspot-modal-content {
    padding: 30px;
}

.booking-details-modal .booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.booking-details-modal .booking-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 700;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-confirmed {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-in_progress {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-completed {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.payment-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.payment-pending {
    background: #fff3cd;
    color: #856404;
}

.payment-partial {
    background: #ffeaa7;
    color: #b7791f;
}

.payment-completed {
    background: #d4edda;
    color: #155724;
}

.payment-failed {
    background: #f8d7da;
    color: #721c24;
}

.booking-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.booking-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.booking-section.full-width {
    grid-column: 1 / -1;
}

.booking-section h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid #0073aa;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-row .label {
    font-weight: 600;
    color: #495057;
    flex: 0 0 40%;
}

.info-row .value {
    color: #2c3e50;
    flex: 1;
    text-align: right;
}

.booking-notes {
    background: #fff;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    color: #495057;
    line-height: 1.5;
}

.booking-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

/* View booking details button styling */
.view-booking-details {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-booking-details:hover {
    background: #005a87;
    transform: translateY(-1px);
}

/* Responsive modal design */
@media (max-width: 768px) {
    .schedspot-modal-overlay {
        padding: 10px;
    }

    .schedspot-modal {
        max-height: 95vh;
    }

    .schedspot-modal-header {
        padding: 15px 20px;
    }

    .schedspot-modal-content {
        padding: 20px;
    }

    .booking-info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .booking-section {
        padding: 15px;
    }

    .booking-actions {
        flex-direction: column;
    }

    .booking-actions .schedspot-btn {
        width: 100%;
        justify-content: center;
    }

    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .info-row .value {
        text-align: left;
    }
}
